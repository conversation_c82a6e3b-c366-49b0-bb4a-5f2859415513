import { test, expect } from '@playwright/test'

test.describe('Global Search Navigation', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/')
    
    // Sign in (assuming we have a test user)
    await page.click('text=Sign In')
    
    // Fill in test credentials - adjust these based on your test setup
    await page.fill('input[name="identifier"]', '<EMAIL>')
    await page.fill('input[name="password"]', 'testpassword')
    await page.click('button[type="submit"]')
    
    // Wait for dashboard to load
    await page.waitForURL('**/dashboard')
    await expect(page.locator('h1')).toContainText('Dashboard')
  })

  test('should navigate to employee appraisal page when clicking on employee in global search', async ({ page }) => {
    console.log('🧪 Starting global search navigation test')
    
    // Open global search using keyboard shortcut
    await page.keyboard.press('Meta+k') // Use Ctrl+k on Windows/Linux
    
    // Wait for search dialog to open
    await expect(page.locator('[role="dialog"]')).toBeVisible()
    console.log('✅ Global search dialog opened')
    
    // Type in search query to find an employee
    const searchInput = page.locator('input[placeholder*="Search employees"]')
    await searchInput.fill('Francesco')
    
    console.log('🔍 Searching for "Francesco"')
    
    // Wait for search results to appear
    await page.waitForTimeout(1000) // Give time for debounced search
    
    // Look for employee results
    const employeeResults = page.locator('[data-testid="search-result-employee"], .command-item:has-text("Employee")')
    
    // If no results with specific test ID, look for any result containing "Francesco"
    const francescoResult = page.locator('.command-item:has-text("Francesco")')
    
    // Check if we have any Francesco results
    const resultCount = await francescoResult.count()
    console.log(`📊 Found ${resultCount} results for Francesco`)
    
    if (resultCount > 0) {
      // Click on the first Francesco result
      await francescoResult.first().click()
      console.log('👆 Clicked on Francesco result')
      
      // Wait for navigation to appraisal page
      await page.waitForURL('**/dashboard/appraisal/**')
      console.log('🎯 Navigated to appraisal page')
      
      // Verify we're on the correct appraisal page
      await expect(page.locator('h1')).toContainText('Appraisal for')
      await expect(page.locator('h1')).toContainText('Francesco')
      
      console.log('✅ Successfully navigated to Francesco\'s appraisal page')
    } else {
      console.log('⚠️ No Francesco results found, trying alternative search')
      
      // Clear and try a different search
      await searchInput.clear()
      await searchInput.fill('test')
      await page.waitForTimeout(1000)
      
      // Look for any employee result
      const anyEmployeeResult = page.locator('.command-item').first()
      
      if (await anyEmployeeResult.isVisible()) {
        await anyEmployeeResult.click()
        console.log('👆 Clicked on first available result')
        
        // Check if we navigated to an appraisal page
        await page.waitForTimeout(2000)
        const currentUrl = page.url()
        console.log(`📍 Current URL: ${currentUrl}`)
        
        if (currentUrl.includes('/dashboard/appraisal/')) {
          await expect(page.locator('h1')).toContainText('Appraisal for')
          console.log('✅ Successfully navigated to an appraisal page')
        } else {
          console.log('ℹ️ Navigated to a different page, which is expected for non-employee results')
        }
      } else {
        console.log('❌ No search results found at all')
        throw new Error('No search results found to test navigation')
      }
    }
  })

  test('should close search dialog after navigation', async ({ page }) => {
    console.log('🧪 Testing search dialog closure after navigation')
    
    // Open global search
    await page.keyboard.press('Meta+k')
    await expect(page.locator('[role="dialog"]')).toBeVisible()
    
    // Search for any employee
    const searchInput = page.locator('input[placeholder*="Search employees"]')
    await searchInput.fill('test')
    await page.waitForTimeout(1000)
    
    // Click on first result if available
    const firstResult = page.locator('.command-item').first()
    if (await firstResult.isVisible()) {
      await firstResult.click()
      
      // Verify dialog is closed
      await expect(page.locator('[role="dialog"]')).not.toBeVisible()
      console.log('✅ Search dialog closed after navigation')
    }
  })

  test('should handle keyboard navigation in search results', async ({ page }) => {
    console.log('🧪 Testing keyboard navigation in search results')
    
    // Open global search
    await page.keyboard.press('Meta+k')
    await expect(page.locator('[role="dialog"]')).toBeVisible()
    
    // Search for employees
    const searchInput = page.locator('input[placeholder*="Search employees"]')
    await searchInput.fill('test')
    await page.waitForTimeout(1000)
    
    // Use arrow keys to navigate
    await page.keyboard.press('ArrowDown')
    await page.keyboard.press('ArrowDown')
    
    // Press Enter to select
    await page.keyboard.press('Enter')
    
    // Wait for navigation
    await page.waitForTimeout(2000)
    
    // Verify we navigated somewhere (dialog should be closed)
    await expect(page.locator('[role="dialog"]')).not.toBeVisible()
    console.log('✅ Keyboard navigation works correctly')
  })
})
